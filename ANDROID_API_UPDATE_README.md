# Atualização do Nível da API Android

## Problema Resolvido

O Google Play exige que todos os apps atendam aos requisitos de nível desejado da API. A partir de 31 de agosto de 2025, se o nível desejado da API não tiver no máximo um ano de diferença em relação à versão mais recente do Android, não será possível atualizar o app.

## Atualizações Realizadas

### ✅ Configurações Atualizadas

#### 1. **android/gradle.properties**
```properties
# Antes
android.compileSdkVersion=34
android.targetSdkVersion=34
android.buildToolsVersion=34.0.0

# Depois
android.compileSdkVersion=35
android.targetSdkVersion=35
android.buildToolsVersion=35.0.0
```

#### 2. **android/build.gradle**
```gradle
// Antes
ext {
    buildToolsVersion = findProperty('android.buildToolsVersion') ?: '33.0.0'
    compileSdkVersion = Integer.parseInt(findProperty('android.compileSdkVersion') ?: '33')
    targetSdkVersion = Integer.parseInt(findProperty('android.targetSdkVersion') ?: '33')
}
dependencies {
    classpath('com.android.tools.build:gradle:7.4.1')
}

// Depois
ext {
    buildToolsVersion = findProperty('android.buildToolsVersion') ?: '35.0.0'
    compileSdkVersion = Integer.parseInt(findProperty('android.compileSdkVersion') ?: '35')
    targetSdkVersion = Integer.parseInt(findProperty('android.targetSdkVersion') ?: '35')
}
dependencies {
    classpath('com.android.tools.build:gradle:7.4.2')
    classpath('com.facebook.react:react-native-gradle-plugin')
}
```

#### 3. **android/gradle/wrapper/gradle-wrapper.properties**
```properties
# Antes
distributionUrl=https\://services.gradle.org/distributions/gradle-7.5.1-all.zip

# Depois
distributionUrl=https\://services.gradle.org/distributions/gradle-7.6.4-all.zip
```

#### 4. **app.json**
```json
// Antes
"android": {
  "compileSdkVersion": 34,
  "targetSdkVersion": 34,
  "buildToolsVersion": "34.0.0"
}

// Depois
"android": {
  "compileSdkVersion": 35,
  "targetSdkVersion": 35,
  "buildToolsVersion": "35.0.0"
}
```

## Versões Atualizadas

| Componente | Versão Anterior | Nova Versão |
|------------|----------------|-------------|
| **Compile SDK** | 34 | 35 |
| **Target SDK** | 34 | 35 |
| **Build Tools** | 34.0.0 | 35.0.0 |
| **Android Gradle Plugin** | 7.4.1 | 7.4.2 |
| **Gradle** | 7.5.1 | 7.6.4 |
| **Kotlin** | - | 1.8.10 |

## Como Testar

### 1. **Limpar o projeto**
```bash
cd android
./gradlew clean
cd ..
```

### 2. **Testar build Android**
```bash
# Desenvolvimento
expo run:android

# Ou usando EAS Build
eas build --platform android
```

### 3. **Verificar se compila sem erros**
- O projeto deve compilar sem erros
- O app deve funcionar normalmente
- Todas as funcionalidades devem continuar operando

### 4. **Testar em dispositivo real**
- Instale o APK em um dispositivo Android
- Teste todas as funcionalidades principais
- Verifique se as permissões funcionam corretamente

## Benefícios da Atualização

### ✅ **Conformidade com Google Play**
- Atende aos requisitos de API level para publicação
- Permite atualizações do app até agosto de 2025 e além
- Evita rejeições na Play Store

### ✅ **Recursos Mais Recentes**
- Acesso às APIs mais recentes do Android
- Melhor performance e segurança
- Suporte a recursos modernos do Android

### ✅ **Compatibilidade**
- Mantém compatibilidade com versões anteriores (minSdkVersion ainda é 21)
- Funciona em dispositivos Android 5.0+ (API 21+)
- Otimizado para dispositivos mais recentes

## Possíveis Problemas e Soluções

### 🔧 **Se o build falhar**

1. **Limpe o cache do Gradle**:
```bash
cd android
./gradlew clean
./gradlew --stop
cd ..
```

2. **Limpe o cache do Metro**:
```bash
npx react-native start --reset-cache
```

3. **Reinstale node_modules**:
```bash
rm -rf node_modules
npm install
```

### 🔧 **Se houver erros de dependências**

Algumas bibliotecas podem precisar ser atualizadas. Execute:
```bash
npm update
```

### 🔧 **Se houver problemas com permissões**

As permissões já foram configuradas corretamente no `AndroidManifest.xml` para suportar as novas APIs.

## Verificação Final

Antes de publicar na Play Store, certifique-se de que:

- [ ] O app compila sem erros
- [ ] Todas as funcionalidades funcionam corretamente
- [ ] As permissões são solicitadas adequadamente
- [ ] O WebView carrega corretamente
- [ ] O loading inicial funciona
- [ ] As compras in-app funcionam (se aplicável)

## Próximos Passos

1. **Teste completo** do app em dispositivos reais
2. **Build de produção** usando EAS Build
3. **Upload para Google Play Console**
4. **Verificação** de que não há avisos sobre API level

## Estratégia de Versões

### **Abordagem Conservadora Adotada**

Usamos uma estratégia conservadora para garantir estabilidade:

- **Target SDK 35**: ✅ Atende aos requisitos do Google Play
- **Gradle 7.6.4**: Versão estável e testada
- **AGP 7.4.2**: Compatível com React Native 0.71.14
- **Kotlin 1.8.10**: Versão sem conflitos

**Por que não usar as versões mais recentes?**
- Gradle 8+ pode ter incompatibilidades com React Native 0.71
- Versões mais antigas são mais estáveis para produção
- O importante é o Target SDK 35, não as ferramentas de build

## Notas Importantes

- **Target SDK 35** corresponde ao Android 15 (API level 35)
- **Compile SDK 35** permite usar as APIs mais recentes
- **Min SDK 21** ainda suportado (Android 5.0+)
- **Gradle 7.6.4** é compatível com Android Gradle Plugin 7.4.2
- **Kotlin 1.8.10** é estável e sem conflitos
- **Build Tools 35.0.0** são as ferramentas mais recentes

Esta atualização garante que o app esteja em conformidade com os requisitos do Google Play e possa ser atualizado sem problemas até pelo menos agosto de 2026.
