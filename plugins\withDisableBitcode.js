const { withXcodeProject } = require('expo/config-plugins');

module.exports = function withDisableBitcode(config) {
  return withXcodeProject(config, async (config) => {
    const xcodeProject = config.modResults;
    
    // Desativa Bitcode para todos os targets e configurações
    Object.entries(xcodeProject.pbxXCBuildConfigurationSection()).forEach(
      ([, buildConfig]) => {
        if (buildConfig.buildSettings) {
          buildConfig.buildSettings.ENABLE_BITCODE = 'NO';
        }
      }
    );

    return config;
  });
};