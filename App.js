import React, { useEffect, useRef, useState } from 'react';
import { StyleSheet, View, BackHandler, RefreshControl, ScrollView, Platform } from 'react-native';
import { WebView } from 'react-native-webview';
import * as RNIap from 'react-native-iap';

const baseUrlApi = 'https://api.mysnapfit.com.br';
const validatePurchaseUrl = baseUrlApi + '/nativeapp/validate-purchase';

export default function App() {
  const webviewRef = useRef(null);
  const [canGoBack, setCanGoBack] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [scrollPosition, setScrollPosition] = useState(0);

  useEffect(() => {
    // Inicializar conexão IAP
    const initIAP = async () => {
      try {
        await RNIap.initConnection();
      } catch (error) {
        console.error('Erro ao inicializar IAP:', error);
      }
    };

    initIAP();

    // Cleanup ao desmontar componente
    return () => {
      RNIap.endConnection();
    };
  }, []);

  useEffect(() => {
    const backAction = () => {
      if (canGoBack && webviewRef.current) {
        webviewRef.current.goBack();
        return true;
      }
      return false;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction
    );

    return () => backHandler.remove();
  }, [canGoBack]);

  const handleNavigationStateChange = (navState) => {
    setCanGoBack(navState.canGoBack);
  };

  const onRefresh = () => {
    setRefreshing(true);
    webviewRef.current.reload();
    setRefreshing(false);
  };

  const onScroll = (e) => {
    const scrollY = e.nativeEvent.contentOffset.y;
    setScrollPosition(scrollY);
  };

  const shouldRefresh = () => {
    return scrollPosition === 0;
  };

  const handleMessage = async (event) => {
    const data = JSON.parse(event.nativeEvent.data);
    if (data.action === 'startPurchase' && data.platform === Platform.OS) {
      const { productId, isSubscription } = data;
      initiatePurchase(productId, isSubscription);
    }
  };

  const initiatePurchase = async (productId, isSubscription) => {
    try {
      if (isSubscription) {
        const subscriptions = await RNIap.getSubscriptions([productId]);
        const result = await RNIap.requestSubscription(productId);
        sendToBackend(result, true);
      } else {
        const products = await RNIap.getProducts([productId]);
        const result = await RNIap.requestPurchase(productId);
        sendToBackend(result, false);
      }
    } catch (error) {
      console.error('Erro na compra:', error);
    }
  };

  const sendToBackend = async (result, isSubscription) => {
    try {
      // Preparar dados da compra baseado na plataforma
      const purchaseData = {
        platform: Platform.OS,
        isSubscription,
        // Propriedades comuns
        productId: result.productId,
        transactionId: result.transactionId,
        transactionDate: result.transactionDate,
        transactionReceipt: result.transactionReceipt,
        purchaseToken: result.purchaseToken, // Android
        // iOS específico
        ...(Platform.OS === 'ios' && {
          originalTransactionIdentifierIOS: result.originalTransactionIdentifierIOS,
          originalTransactionDateIOS: result.originalTransactionDateIOS,
        }),
        // Android específico
        ...(Platform.OS === 'android' && {
          dataAndroid: result.dataAndroid,
          signatureAndroid: result.signatureAndroid,
        })
      };

      const response = await fetch(validatePurchaseUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(purchaseData)
      });

      const responseData = await response.json();
      console.log('Resposta do backend:', responseData);

      // Finalizar compra após validação bem-sucedida
      if (response.ok && responseData.success) {
        await RNIap.finishTransaction(result, isSubscription);
      }
    } catch (error) {
      console.error('Erro ao validar compra:', error);
    }
  };

  const platform = Platform.OS;
  const webviewUrl = `https://app.mysnapfit.com.br/webview?platform=${platform}`;

  return (
    <ScrollView
      contentContainerStyle={styles.scrollViewContainer}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          enabled={shouldRefresh()}
        />
      }
    >
      <View style={styles.container}>
        <WebView
          ref={webviewRef}
          source={{ uri: webviewUrl }}
          style={styles.webview}
          onNavigationStateChange={handleNavigationStateChange}
          onScroll={onScroll}
          scrollEnabled={true}
          onMessage={handleMessage}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollViewContainer: {
    flex: 1
  },
  container: {
    flex: 1,
  },
  webview: {
    flex: 1,
    height: 500,
  },
});