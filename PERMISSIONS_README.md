# Configuração de Permissões - React Native/Expo

Este projeto foi configurado com todas as permissões necessárias para:
- 📷 Captura de fotos e vídeos
- 🎤 Gravação de áudio
- 📁 Acesso e seleção de arquivos
- 🖼️ Acesso à galeria de fotos

## Permissões Configuradas

### Android (AndroidManifest.xml)
- `CAMERA` - <PERSON><PERSON> à câmera
- `RECORD_AUDIO` - Gravação de áudio
- `READ_MEDIA_IMAGES` - Leitura de imagens (Android 13+)
- `READ_MEDIA_VIDEO` - Leitura de vídeos (Android 13+)
- `READ_MEDIA_AUDIO` - Leitura de áudio (Android 13+)
- `READ_EXTERNAL_STORAGE` - Leitura do armazenamento externo
- `WRITE_EXTERNAL_STORAGE` - Escrita no armazenamento externo
- `ACCESS_MEDIA_LOCATION` - Acesso à localização de mídia

### iOS (app.json)
- `NSCameraUsageDescription` - <PERSON><PERSON> à câmera
- `NSMicrophoneUsageDescription` - Acesso ao microfone
- `NSPhotoLibraryUsageDescription` - Acesso à galeria de fotos
- `NSPhotoLibraryAddUsageDescription` - Salvar na galeria
- `NSDocumentsFolderUsageDescription` - Acesso a documentos
- `NSDownloadsFolderUsageDescription` - Acesso a downloads

## Bibliotecas Instaladas

```json
{
  "expo-camera": "^16.1.10",
  "expo-av": "^15.1.7",
  "expo-document-picker": "^13.1.6",
  "expo-image-picker": "^16.1.4",
  "expo-media-library": "^17.1.7"
}
```

## Como Usar

### 1. Importar as bibliotecas necessárias

```javascript
import { Camera } from 'expo-camera';
import { Audio } from 'expo-av';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';
```

### 2. Solicitar permissões

```javascript
// Câmera
const cameraPermission = await Camera.requestCameraPermissionsAsync();

// Áudio
const audioPermission = await Audio.requestPermissionsAsync();

// Biblioteca de mídia
const mediaPermission = await MediaLibrary.requestPermissionsAsync();
```

### 3. Usar as funcionalidades

#### Tirar foto
```javascript
const result = await ImagePicker.launchCameraAsync({
  mediaTypes: ImagePicker.MediaTypeOptions.Images,
  allowsEditing: true,
  aspect: [4, 3],
  quality: 1,
});
```

#### Gravar vídeo
```javascript
const result = await ImagePicker.launchCameraAsync({
  mediaTypes: ImagePicker.MediaTypeOptions.Videos,
  allowsEditing: true,
  quality: 1,
});
```

#### Gravar áudio
```javascript
await Audio.setAudioModeAsync({
  allowsRecordingIOS: true,
  playsInSilentModeIOS: true,
});

const { recording } = await Audio.Recording.createAsync(
  Audio.RecordingOptionsPresets.HIGH_QUALITY
);
```

#### Selecionar arquivo
```javascript
const result = await DocumentPicker.getDocumentAsync({
  type: '*/*',
  copyToCacheDirectory: true,
});
```

#### Selecionar da galeria
```javascript
const result = await ImagePicker.launchImageLibraryAsync({
  mediaTypes: ImagePicker.MediaTypeOptions.Images,
  allowsEditing: true,
  aspect: [4, 3],
  quality: 1,
});
```

## Arquivo de Exemplo

Veja o arquivo `PermissionsExample.js` para um exemplo completo de como implementar todas essas funcionalidades.

## Próximos Passos

1. **Rebuild do projeto**: Após adicionar as permissões, é necessário fazer rebuild do projeto:
   ```bash
   expo run:android
   expo run:ios
   ```

2. **Teste em dispositivo real**: As permissões só funcionam em dispositivos reais, não no simulador.

3. **Tratamento de erros**: Sempre implemente tratamento de erros adequado para quando as permissões são negadas.

## Notas Importantes

- As permissões são solicitadas automaticamente quando você tenta usar a funcionalidade
- No iOS, as descrições das permissões são obrigatórias e devem explicar claramente por que o app precisa de cada permissão
- No Android 13+, as permissões de mídia são mais granulares (imagens, vídeos, áudio separadamente)
- Sempre teste em dispositivos reais, pois simuladores podem não refletir o comportamento real das permissões
