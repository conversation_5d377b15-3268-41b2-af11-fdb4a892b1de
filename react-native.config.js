module.exports = {
  dependencies: {
    // Disable Expo modules autolinking to avoid SSL/TLS issues
    'expo': {
      platforms: {
        android: {
          sourceDir: null,
          packageImportPath: null,
        },
      },
    },
    'expo-av': {
      platforms: {
        android: {
          sourceDir: null,
          packageImportPath: null,
        },
      },
    },
    'expo-camera': {
      platforms: {
        android: {
          sourceDir: null,
          packageImportPath: null,
        },
      },
    },
    'expo-document-picker': {
      platforms: {
        android: {
          sourceDir: null,
          packageImportPath: null,
        },
      },
    },
    'expo-image-picker': {
      platforms: {
        android: {
          sourceDir: null,
          packageImportPath: null,
        },
      },
    },
    'expo-media-library': {
      platforms: {
        android: {
          sourceDir: null,
          packageImportPath: null,
        },
      },
    },
    'expo-splash-screen': {
      platforms: {
        android: {
          sourceDir: null,
          packageImportPath: null,
        },
      },
    },
    'expo-status-bar': {
      platforms: {
        android: {
          sourceDir: null,
          packageImportPath: null,
        },
      },
    },
    'expo-build-properties': {
      platforms: {
        android: {
          sourceDir: null,
          packageImportPath: null,
        },
      },
    },
  },
};
