{"name": "snapfit", "version": "1.0.0", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"expo": "~48.0.21", "expo-av": "^15.1.7", "expo-build-properties": "~0.6.0", "expo-camera": "^16.1.10", "expo-document-picker": "^13.1.6", "expo-image-picker": "^16.1.4", "expo-media-library": "^17.1.7", "expo-modules-core": "^2.4.2", "expo-splash-screen": "~0.18.2", "expo-status-bar": "~1.4.4", "react": "18.2.0", "react-native": "0.71.14", "react-native-iap": "^12.10.7", "react-native-webview": "11.26.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@expo/config-plugins": "^10.0.3"}, "private": true}