# WebView Loading Implementation

## Implementação Realizada

Foi adicionado um loading overlay que aparece **apenas durante o carregamento inicial** do WebView, resolvendo o problema da tela em branco que aparecia ao abrir o app.

## Características da Implementação

### ✅ O que foi implementado:

1. **Loading Overlay Simples**
   - Ícone de loading (ActivityIndicator) sem texto
   - Cor azul (#007AFF) seguindo padrões iOS/Android
   - Fundo branco cobrindo toda a tela

2. **Controle Inteligente**
   - Aparece apenas no carregamento inicial do app
   - **NÃO aparece** durante navegação entre páginas
   - Desaparece automaticamente quando o WebView termina de carregar
   - Desaparece também em caso de erro de carregamento

3. **Posicionamento**
   - Overlay absoluto sobre o WebView
   - Centralizado na tela
   - Z-index alto (1000) para ficar sempre visível

## Código Implementado

### Estado de Loading
```javascript
const [isInitialLoading, setIsInitialLoading] = useState(true);
```

### Componente de Loading
```javascript
const LoadingOverlay = () => (
  <View style={styles.loadingOverlay}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);
```

### Handlers do WebView
```javascript
const handleLoadEnd = () => {
  // Esconder loading após carregamento inicial completo
  setIsInitialLoading(false);
};

const handleLoadError = () => {
  // Esconder loading em caso de erro
  setIsInitialLoading(false);
};
```

### Integração no WebView
```javascript
<WebView
  // ... outras props
  onLoadStart={handleLoadStart}
  onLoadEnd={handleLoadEnd}
  onError={handleLoadError}
/>
{isInitialLoading && <LoadingOverlay />}
```

### Estilos
```javascript
loadingOverlay: {
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: '#ffffff',
  justifyContent: 'center',
  alignItems: 'center',
  zIndex: 1000,
}
```

## Como Testar

### 1. Carregamento Inicial
- Abra o app
- Deve aparecer o loading imediatamente
- Loading desaparece quando o WebView carrega completamente
- **Resultado esperado**: Não há mais tela em branco

### 2. Navegação Entre Páginas
- Navegue para diferentes páginas dentro do WebView
- **Resultado esperado**: Loading NÃO deve aparecer
- O app web já possui seus próprios loadings internos

### 3. Teste de Erro
- Desconecte a internet
- Abra o app
- **Resultado esperado**: Loading desaparece quando ocorre erro de rede

### 4. Teste de Refresh
- Use o pull-to-refresh
- **Resultado esperado**: Loading NÃO deve aparecer (apenas o refresh nativo)

## Comportamento Detalhado

| Situação | Loading Aparece? | Motivo |
|----------|------------------|---------|
| Abertura inicial do app | ✅ SIM | Evita tela em branco |
| Navegação entre páginas | ❌ NÃO | App web já tem loading |
| Pull-to-refresh | ❌ NÃO | Usa RefreshControl nativo |
| Erro de carregamento | ✅ SIM (depois desaparece) | Feedback visual |
| Reconexão após erro | ❌ NÃO | Não é carregamento inicial |

## Vantagens da Implementação

1. **UX Melhorada**: Elimina a tela em branco inicial
2. **Performance**: Loading leve usando ActivityIndicator nativo
3. **Não Intrusivo**: Não interfere com navegação interna do app web
4. **Responsivo**: Funciona em qualquer tamanho de tela
5. **Acessível**: Usa componentes nativos com acessibilidade

## Customizações Possíveis

Se quiser personalizar o loading, você pode:

### Mudar a Cor
```javascript
<ActivityIndicator size="large" color="#FF6B35" />
```

### Adicionar Logo
```javascript
const LoadingOverlay = () => (
  <View style={styles.loadingOverlay}>
    <Image source={require('./assets/logo.png')} style={styles.logo} />
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);
```

### Mudar Fundo
```javascript
loadingOverlay: {
  // ... outras propriedades
  backgroundColor: '#f8f9fa', // Fundo cinza claro
}
```

## Notas Técnicas

- O loading usa `position: 'absolute'` para sobrepor o WebView
- O estado `isInitialLoading` começa como `true` e muda para `false` apenas uma vez
- Os eventos `onLoadEnd` e `onError` garantem que o loading sempre desaparece
- O `zIndex: 1000` garante que o loading fique sempre visível
