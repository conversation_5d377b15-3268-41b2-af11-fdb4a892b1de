import React, { useState, useEffect } from 'react';
import { View, Text, Button, Alert, StyleSheet, Image } from 'react-native';
import { Camera } from 'expo-camera';
import { Audio } from 'expo-av';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';

export default function PermissionsExample() {
  const [cameraPermission, setCameraPermission] = useState(null);
  const [audioPermission, setAudioPermission] = useState(null);
  const [mediaLibraryPermission, setMediaLibraryPermission] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [recording, setRecording] = useState(null);
  const [isRecording, setIsRecording] = useState(false);

  useEffect(() => {
    checkPermissions();
  }, []);

  const checkPermissions = async () => {
    // Verificar permissões da câmera
    const cameraStatus = await Camera.requestCameraPermissionsAsync();
    setCameraPermission(cameraStatus.status === 'granted');

    // Verificar permissões de áudio
    const audioStatus = await Audio.requestPermissionsAsync();
    setAudioPermission(audioStatus.status === 'granted');

    // Verificar permissões da biblioteca de mídia
    const mediaLibraryStatus = await MediaLibrary.requestPermissionsAsync();
    setMediaLibraryPermission(mediaLibraryStatus.status === 'granted');
  };

  const takePicture = async () => {
    if (!cameraPermission) {
      Alert.alert('Erro', 'Permissão da câmera não concedida');
      return;
    }

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled) {
        setSelectedImage(result.assets[0].uri);
        Alert.alert('Sucesso', 'Foto capturada com sucesso!');
      }
    } catch (error) {
      Alert.alert('Erro', 'Erro ao capturar foto: ' + error.message);
    }
  };

  const recordVideo = async () => {
    if (!cameraPermission) {
      Alert.alert('Erro', 'Permissão da câmera não concedida');
      return;
    }

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Videos,
        allowsEditing: true,
        quality: 1,
      });

      if (!result.canceled) {
        Alert.alert('Sucesso', 'Vídeo gravado com sucesso!');
      }
    } catch (error) {
      Alert.alert('Erro', 'Erro ao gravar vídeo: ' + error.message);
    }
  };

  const startRecording = async () => {
    if (!audioPermission) {
      Alert.alert('Erro', 'Permissão de áudio não concedida');
      return;
    }

    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );
      setRecording(recording);
      setIsRecording(true);
      Alert.alert('Gravação', 'Gravação de áudio iniciada');
    } catch (error) {
      Alert.alert('Erro', 'Erro ao iniciar gravação: ' + error.message);
    }
  };

  const stopRecording = async () => {
    if (!recording) return;

    try {
      setIsRecording(false);
      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();
      setRecording(null);
      Alert.alert('Sucesso', 'Gravação de áudio finalizada!');
    } catch (error) {
      Alert.alert('Erro', 'Erro ao parar gravação: ' + error.message);
    }
  };

  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled) {
        Alert.alert('Sucesso', `Arquivo selecionado: ${result.assets[0].name}`);
      }
    } catch (error) {
      Alert.alert('Erro', 'Erro ao selecionar arquivo: ' + error.message);
    }
  };

  const pickImageFromGallery = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled) {
        setSelectedImage(result.assets[0].uri);
        Alert.alert('Sucesso', 'Imagem selecionada da galeria!');
      }
    } catch (error) {
      Alert.alert('Erro', 'Erro ao selecionar imagem: ' + error.message);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Teste de Permissões</Text>
      
      <View style={styles.permissionStatus}>
        <Text>Câmera: {cameraPermission ? '✅ Permitida' : '❌ Negada'}</Text>
        <Text>Áudio: {audioPermission ? '✅ Permitida' : '❌ Negada'}</Text>
        <Text>Galeria: {mediaLibraryPermission ? '✅ Permitida' : '❌ Negada'}</Text>
      </View>

      <View style={styles.buttonContainer}>
        <Button title="Tirar Foto" onPress={takePicture} />
        <Button title="Gravar Vídeo" onPress={recordVideo} />
        <Button 
          title={isRecording ? "Parar Gravação" : "Gravar Áudio"} 
          onPress={isRecording ? stopRecording : startRecording} 
        />
        <Button title="Selecionar Arquivo" onPress={pickDocument} />
        <Button title="Selecionar da Galeria" onPress={pickImageFromGallery} />
      </View>

      {selectedImage && (
        <Image source={{ uri: selectedImage }} style={styles.image} />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  permissionStatus: {
    marginBottom: 20,
    padding: 10,
    backgroundColor: '#f0f0f0',
    borderRadius: 5,
  },
  buttonContainer: {
    gap: 10,
    marginBottom: 20,
  },
  image: {
    width: 200,
    height: 200,
    alignSelf: 'center',
    borderRadius: 10,
  },
});
