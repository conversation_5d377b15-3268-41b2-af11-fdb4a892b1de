{"expo": {"name": "Snapfit", "slug": "snapfit", "version": "1.1.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bitcode": false, "bundleIdentifier": "br.com.mysnapfit", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}, "jsEngine": "jsc"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "br.com.mysnapfit"}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "e0d3e04e-6a64-46b6-a0b0-9070d8654d56"}}, "owner": "hitvox", "plugins": [["./plugins/withDisableBitcode", {"android": {"compileSdkVersion": 34, "targetSdkVersion": 34, "buildToolsVersion": "34.0.0"}}]], "cli": {"appVersionSource": "remote"}}}