{"expo": {"name": "Snapfit", "slug": "snapfit", "version": "1.1.1", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bitcode": false, "bundleIdentifier": "br.com.mysnapfit", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSCameraUsageDescription": "Este aplicativo precisa acessar a câmera para capturar fotos e vídeos.", "NSMicrophoneUsageDescription": "Este aplicativo precisa acessar o microfone para gravar áudio e vídeos.", "NSPhotoLibraryUsageDescription": "Este aplicativo precisa acessar a galeria de fotos para selecionar imagens e vídeos.", "NSPhotoLibraryAddUsageDescription": "Este aplicativo precisa salvar fotos e vídeos na galeria.", "NSDocumentsFolderUsageDescription": "Este aplicativo precisa acessar documentos para selecionar arquivos.", "NSDownloadsFolderUsageDescription": "Este aplicativo precisa acessar a pasta de downloads para selecionar arquivos."}, "jsEngine": "jsc"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "br.com.mysnapfit"}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "e0d3e04e-6a64-46b6-a0b0-9070d8654d56"}}, "owner": "hitvox", "plugins": [["./plugins/withDisableBitcode", {"android": {"compileSdkVersion": 35, "targetSdkVersion": 35, "buildToolsVersion": "35.0.0"}}]], "cli": {"appVersionSource": "remote"}}}