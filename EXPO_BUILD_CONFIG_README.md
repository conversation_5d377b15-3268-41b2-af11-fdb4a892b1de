# Configuração de Build Expo - Android API 35

## Problema Identificado

O projeto usa Expo SDK 48 que requer configurações específicas para funcionar com Android API 35. Os erros encontrados foram:

1. **Plugin Expo não encontrado**: `expo-module-gradle-plugin` não estava configurado corretamente
2. **compileSdkVersion não especificado**: Configurações do Expo não estavam sendo aplicadas

## Solução Implementada

### ✅ **Configuração do Plugin Expo**

#### **android/build.gradle**
```gradle
buildscript {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://maven.expo.dev' }
        maven { url 'https://www.jitpack.io' } // Para Expo modules
    }
    dependencies {
        classpath('com.android.tools.build:gradle:7.4.2')
        classpath('com.facebook.react:react-native-gradle-plugin')
        classpath('expo:expo-module-gradle-plugin:2.0.0') // Compatível com SDK 48
    }
}
```

#### **android/app/build.gradle**
```gradle
apply plugin: "com.android.application"
apply plugin: "com.facebook.react"
apply plugin: "expo-module-gradle-plugin" // Plugin do Expo
```

#### **android/gradle.properties**
```properties
# Configurações principais
android.compileSdkVersion=35
android.targetSdkVersion=35
android.buildToolsVersion=35.0.0

# Configurações específicas do Expo
expo.compileSdkVersion=35
expo.targetSdkVersion=35
expo.buildToolsVersion=35.0.0

# Kotlin
android.kotlinVersion=1.8.10
```

## Versões Compatíveis

| Componente | Versão | Compatibilidade |
|------------|--------|----------------|
| **Expo SDK** | 48.0.21 | ✅ Base do projeto |
| **Expo Plugin** | 2.0.0 | ✅ Compatível com SDK 48 |
| **Target SDK** | 35 | ✅ Requisito Google Play |
| **Compile SDK** | 35 | ✅ APIs mais recentes |
| **Gradle** | 7.6.4 | ✅ Estável |
| **AGP** | 7.4.2 | ✅ Compatível |

## Estrutura de Arquivos

### **settings.gradle**
```gradle
rootProject.name = 'Snapfit'

// Expo autolinking - requer o plugin
apply from: new File(["node", "--print", "require.resolve('expo/package.json')"].execute(null, rootDir).text.trim(), "../scripts/autolinking.gradle");
useExpoModules() // ← Requer expo-module-gradle-plugin

// React Native
apply from: new File(["node", "--print", "require.resolve('@react-native-community/cli-platform-android/package.json')"].execute(null, rootDir).text.trim(), "../native_modules.gradle");
applyNativeModulesSettingsGradle(settings)

include ':app'
includeBuild(new File(["node", "--print", "require.resolve('react-native-gradle-plugin/package.json')"].execute(null, rootDir).text.trim()).getParentFile())
```

## Dependências Expo Ativas

```json
{
  "expo": "~48.0.21",
  "expo-av": "^15.1.7",
  "expo-build-properties": "~0.6.0",
  "expo-camera": "^16.1.10",
  "expo-document-picker": "^13.1.6",
  "expo-image-picker": "^16.1.4",
  "expo-media-library": "^17.1.7",
  "expo-splash-screen": "~0.18.2",
  "expo-status-bar": "~1.4.4"
}
```

## Como Funciona

### **1. Expo Autolinking**
- O `settings.gradle` chama `useExpoModules()`
- Isso requer o `expo-module-gradle-plugin`
- O plugin configura automaticamente os módulos Expo

### **2. Configuração de SDK**
- `android.compileSdkVersion=35` define a versão principal
- `expo.compileSdkVersion=35` garante que o Expo use a mesma versão
- Evita conflitos entre configurações

### **3. Plugin Compatibility**
- Expo SDK 48 requer plugin versão 2.0.0
- Versões mais recentes podem não ser compatíveis
- Gradle 7.6.4 é estável para esta combinação

## Teste da Configuração

### **1. Verificar Plugin**
```bash
cd android
./gradlew tasks --all | grep expo
```

### **2. Build Local**
```bash
./gradlew clean
./gradlew assembleRelease
```

### **3. Build EAS**
```bash
eas build --platform android
```

## Possíveis Problemas

### **❌ Plugin não encontrado**
```
Plugin [id: 'expo-module-gradle-plugin'] was not found
```
**Solução**: Verificar se o plugin está no `dependencies` do `build.gradle`

### **❌ compileSdkVersion não especificado**
```
compileSdkVersion is not specified. Please add it to build.gradle
```
**Solução**: Adicionar configurações `expo.*` no `gradle.properties`

### **❌ Versão incompatível**
```
Could not resolve expo:expo-module-gradle-plugin:X.X.X
```
**Solução**: Usar versão 2.0.0 compatível com Expo SDK 48

## Benefícios da Configuração

✅ **Expo Modules**: Todos os módulos Expo funcionam corretamente  
✅ **Target SDK 35**: Atende aos requisitos do Google Play  
✅ **Autolinking**: Configuração automática de dependências  
✅ **Estabilidade**: Versões testadas e compatíveis  
✅ **Funcionalidades**: Câmera, áudio, arquivos, WebView, etc.  

## Próximos Passos

1. **Teste local**: `./gradlew assembleRelease`
2. **Teste EAS**: `eas build --platform android`
3. **Verificar funcionalidades**: Todas as features do app
4. **Upload Play Store**: Com Target SDK 35

## Notas Importantes

- **Expo SDK 48** é uma versão estável e bem testada
- **Plugin 2.0.0** é específico para esta versão do SDK
- **Target SDK 35** é o que importa para o Google Play
- **Gradle 7.6.4** oferece melhor compatibilidade que versões 8+

Esta configuração garante que o projeto Expo funcione corretamente com Android API 35! 🎉
