rootProject.name = 'Snapfit'

// Autolinking disabled to avoid Expo modules issues
// apply from: new File(["node", "--print", "require.resolve('@react-native-community/cli-platform-android/package.json')"].execute(null, rootDir).text.trim(), "../native_modules.gradle");
// applyNativeModulesSettingsGradle(settings)

// Manual inclusion of required React Native modules
include ':react-native-webview'
project(':react-native-webview').projectDir = new File(rootDir, '../node_modules/react-native-webview/android')

include ':react-native-iap'
project(':react-native-iap').projectDir = new File(rootDir, '../node_modules/react-native-iap/android')

include ':app'
includeBuild(new File(["node", "--print", "require.resolve('react-native-gradle-plugin/package.json')"].execute(null, rootDir).text.trim()).getParentFile())
